name: Release MacOS Build
permissions:
  contents: write

on:
  push:
    tags:
      - "*"
jobs:
  release:
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: "web/package-lock.json"
      - name: Build Frontend
        run: npm install && VITE_VERSION=${{ github.ref_name }} npm run build
        working-directory: ./web
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23.x"
      - name: Build Backend arm64
        run: |
          go mod download
          go build -ldflags "-s -w -X gpt-load/internal/version.Version=${{ github.ref_name }}" -o gpt-load-macos-arm64
        env:
          CGO_ENABLED: 0

      - name: Build Backend amd64
        run: |
          go mod download
          CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags "-s -w -X gpt-load/internal/version.Version=${{ github.ref_name }}" -o gpt-load-macos-amd64

      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            gpt-load-macos-arm64
            gpt-load-macos-amd64
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
