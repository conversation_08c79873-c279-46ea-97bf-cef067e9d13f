# Use pnpm as package manager
package-manager=pnpm

# Registry configuration
registry=https://registry.npmjs.org/

# Install configuration
prefer-offline=true
prefer-frozen-lockfile=true
strict-peer-dependencies=false

# Hoisting configuration
shamefully-hoist=false
public-hoist-pattern[]=*eslint*
public-hoist-pattern[]=*prettier*

# Workspace configuration
link-workspace-packages=true

# Node.js configuration
node-version=18

# Store configuration
store-dir=~/.pnpm-store

# Logging
loglevel=info

# Security
audit-level=moderate
